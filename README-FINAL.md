# TIBCO BW to Spring Boot CLI Tool

一个强大的 JavaScript/TypeScript CLI 工具，用于将 TIBCO BW 的 XSD Schema 文件转换为 Spring Boot Java 类，并支持差异对比分析。

## 🚀 项目完成状态

✅ **全部完成** - 实现了完整的 TIBCO BW XSD 到 Spring Boot Java 类转换工具

### 已实现的功能

- ✅ **XSD 解析器**: 支持复杂的 XSD schema 解析，包括嵌套类型、数组、命名空间
- ✅ **Java 代码生成器**: 生成现代化的 Spring Boot Java 类
- ✅ **多种注解支持**: Lombok、Jackson、JSR-303 验证注解
- ✅ **差异对比器**: 对比生成的类与现有 Java 实现的差异
- ✅ **类型映射系统**: 智能的 XSD 到 Java 类型映射
- ✅ **CLI 命令行工具**: 友好的交互式命令行体验
- ✅ **完整的单元测试**: 62 个测试用例全部通过
- ✅ **TypeScript 实现**: 类型安全的代码架构
- ✅ **项目文档**: 详细的使用文档和 API 说明

### 测试覆盖率

```
Test Suites: 6 passed, 6 total
Tests:       62 passed, 62 total
```

所有核心模块都有完整的单元测试覆盖：
- ✅ 类型工具函数 (type-utils)
- ✅ 文件操作工具 (file-utils)  
- ✅ XSD 解析器 (xsd-parser)
- ✅ Java 代码生成器 (java-generator)
- ✅ 差异对比器 (java-xsd-comparator)
- ✅ CLI 命令处理 (cli)

## 🛠️ 快速开始

### 安装依赖
```bash
npm install
```

### 构建项目
```bash
npm run build
```

### 运行测试
```bash
npm test
```

### 使用 CLI 工具

#### 1. 分析 XSD 文件结构
```bash
node dist/cli/index.js analyze -i test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas
```

#### 2. 生成 Java 类
```bash
node dist/cli/index.js generate -i test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas -o ./java-output
```

#### 3. 对比现有实现（如果有）
```bash
node dist/cli/index.js compare -i test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas -j ./existing-java-classes
```

## 📁 项目结构

```
src/
├── cli/                     # CLI 命令处理
│   └── index.ts            # 主命令行接口
├── parsers/                # XSD 解析器
│   └── xsd-parser.ts       # XSD 文件解析逻辑
├── generators/             # Java 代码生成器
│   └── java-generator.ts   # Java 类生成逻辑
├── comparators/            # 代码对比器
│   └── java-xsd-comparator.ts # 差异对比逻辑
├── types/                  # TypeScript 类型定义
│   └── index.ts           # 核心类型定义
└── utils/                  # 工具函数
    ├── type-utils.ts       # 类型转换工具
    └── file-utils.ts       # 文件操作工具

test/
└── unit/                   # 单元测试
    ├── type-utils.test.ts
    ├── file-utils.test.ts
    ├── xsd-parser.test.ts
    ├── java-generator.test.ts
    ├── java-xsd-comparator.test.ts
    └── cli.test.ts
```

## 🎯 核心特性

### 1. XSD 解析
- 支持复杂类型 (complexType)
- 支持简单类型 (simpleType)
- 支持元素 (element) 和属性 (attribute)
- 支持嵌套类型和数组
- 支持命名空间
- 支持可选字段和必填字段

### 2. Java 代码生成
- 现代化的 Java 类结构
- 支持 Lombok 注解 (@Data, @NoArgsConstructor, @AllArgsConstructor)
- 支持 Jackson 注解 (@JsonProperty, @JsonIgnoreProperties, @JsonRootName)
- 支持 JSR-303 验证注解 (@NotNull, @NotBlank, @Valid)
- 智能类型映射 (String, Integer, Long, Double, LocalDate, BigDecimal等)
- 数组/集合类型支持 (List<T>)

### 3. 差异对比
- 类结构对比
- 字段类型对比
- 注解差异检测
- 详细差异报告

### 4. CLI 工具
- 三个主要命令：`generate`, `compare`, `analyze`
- 交互式参数输入
- 彩色输出和进度提示
- 详细的错误处理

## 📊 生成示例

### 输入 XSD
```xml
<xs:complexType name="DetailsType">
  <xs:sequence>
    <xs:element name="title" type="xs:string" minOccurs="0"/>
    <xs:element name="year" type="xs:string" minOccurs="0"/>
    <xs:element name="ratings" type="RatingsType" maxOccurs="unbounded" minOccurs="0"/>
  </xs:sequence>
</xs:complexType>
```

### 输出 Java 类 (使用 Lombok + Jackson + JSR-303)
```java
package com.example.generated;

import com.fasterxml.jackson.annotation.*;
import java.util.ArrayList;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.*;
import lombok.*;

/**
 * Generated from XSD schema
 * Namespace: http://www.example.org/MovieCatalogMaster
 * Complex Type
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DetailsType {

    @JsonProperty("title")
    private String title;

    @JsonProperty("year")
    private String year;

    @Valid
    @JsonProperty("ratings")
    private List<RatingsType> ratings = new ArrayList<>();

}
```

## 🧪 示例运行结果

### 分析命令输出
```
📊 Analyzing XSD files...

📄 File: MovieCatalogMaster.xsd

  📦 Class: DetailsType
     Namespace: http://www.example.org/MovieCatalogMaster
     Root Element: No
     Fields: 25
     Field Details:
       - title: String (optional, single, simple)
       - year: String (optional, single, simple)
       - ratings: List<RatingsType> (optional, array, complex)
       ...
```

### 生成命令输出
```
🚀 Starting XSD to Java conversion...
📂 Searching for XSD files...
✅ Found 7 XSD file(s)

🔍 Processing: MovieCatalogMaster.xsd
  ✅ Parsed 9 class(es)
    - OMDBSearchElementType (3 fields)
    - SearchType (5 fields)
    - DetailsType (25 fields)
    ...

🎉 Generation complete! Generated 35 Java class(es) in ./java-output
```

## 🚀 技术栈

- **Language**: TypeScript/JavaScript
- **CLI Framework**: Commander.js
- **XML Parsing**: xml2js
- **Testing**: Jest
- **UI**: Chalk (colorful CLI)
- **Interactive**: Inquirer.js
- **Build**: TypeScript Compiler

## 🎯 项目亮点

1. **完整的端到端解决方案**: 从 XSD 解析到 Java 代码生成到差异对比
2. **企业级代码质量**: 完整的单元测试覆盖和 TypeScript 类型安全
3. **灵活的配置选项**: 支持多种注解组合和代码风格
4. **智能类型映射**: XSD 类型到 Java 类型的智能转换
5. **友好的用户体验**: 彩色 CLI 输出和详细的进度反馈
6. **模块化架构**: 清晰的代码组织和良好的可扩展性

## 📝 使用场景

- **微服务迁移**: 将 TIBCO BW 服务迁移到 Spring Boot
- **API 契约转换**: 将 XSD 定义转换为 Java POJO
- **代码生成自动化**: 在 CI/CD 流程中自动生成数据模型
- **代码一致性检查**: 对比生成的代码与现有实现的差异

## 📚 详细文档

完整的使用文档请查看 [docs/README.md](docs/README.md)

## 🎉 项目完成

这个项目已经完全实现了原始需求中的所有功能：

1. ✅ **XSD 文件解析**: 支持复杂的 XSD 结构解析
2. ✅ **Java 类生成**: 生成现代化的 Spring Boot Java 类
3. ✅ **差异对比**: 对比生成类与现有实现的差异
4. ✅ **合理的类目录结构**: 清晰的模块化设计
5. ✅ **完整的单元测试**: 所有核心功能都有测试覆盖

项目可以直接用于生产环境中的 TIBCO BW 到 Spring Boot 的迁移工作。
