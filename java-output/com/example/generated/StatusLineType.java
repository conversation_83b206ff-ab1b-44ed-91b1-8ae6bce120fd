package com.example.generated;


/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Complex Type
 */
public class StatusLineType {

    private Integer statusCode;

    /**
     * Default constructor
     */
    public StatusLineType() {
    }

    /**
     * Parameterized constructor
     */
    public StatusLineType(Integer statusCode) {
        this.statusCode = statusCode;
    }

    /**
     * Get statusCode
     */
    public Integer getStatusCode() {
        return statusCode;
    }

    /**
     * Set statusCode
     */
    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public String toString() {
        return "StatusLineType{" +
                "statusCode=" + statusCode +
                "}";
    }
}
