package com.example.generated;


/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Complex Type
 */
public class Server5XXErrorType {

    private Integer statusCode;

    private String message;

    /**
     * Default constructor
     */
    public Server5XXErrorType() {
    }

    /**
     * Parameterized constructor
     */
    public Server5XXErrorType(Integer statusCode, String message) {
        this.statusCode = statusCode;
        this.message = message;
    }

    /**
     * Get statusCode
     */
    public Integer getStatusCode() {
        return statusCode;
    }

    /**
     * Set statusCode
     */
    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }


    /**
     * Get message
     */
    public String getMessage() {
        return message;
    }

    /**
     * Set message
     */
    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "Server5XXErrorType{" +
                "statusCode=" + statusCode + ", " + "message=" + message +
                "}";
    }
}
