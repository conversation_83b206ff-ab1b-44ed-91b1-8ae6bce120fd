package com.example.generated;


/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Complex Type
 */
public class DynamicHeadersTypeDetails {

    private String name;

    private String value;

    /**
     * Default constructor
     */
    public DynamicHeadersTypeDetails() {
    }

    /**
     * Parameterized constructor
     */
    public DynamicHeadersTypeDetails(String name, String value) {
        this.name = name;
        this.value = value;
    }

    /**
     * Get name
     */
    public String getName() {
        return name;
    }

    /**
     * Set name
     */
    public void setName(String name) {
        this.name = name;
    }


    /**
     * Get value
     */
    public String getValue() {
        return value;
    }

    /**
     * Set value
     */
    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "DynamicHeadersTypeDetails{" +
                "name=" + name + ", " + "value=" + value +
                "}";
    }
}
