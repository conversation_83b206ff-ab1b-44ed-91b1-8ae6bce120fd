package com.example.generated;
import java.util.ArrayList;
import java.util.List;

/**
 * Generated from XSD schema
 * Namespace: http://www.example.org/MovieCatalogMaster
 * Complex Type
 */
public class OMDBSearchElementType {

    private List<SearchType> search = new ArrayList<>();

    private String totalResults;

    private String response;

    /**
     * Default constructor
     */
    public OMDBSearchElementType() {
    }

    /**
     * Parameterized constructor
     */
    public OMDBSearchElementType(List<SearchType> search, String totalResults, String response) {
        this.search = search;
        this.totalResults = totalResults;
        this.response = response;
    }

    /**
     * Get search
     */
    public List<SearchType> getSearch() {
        return search;
    }

    /**
     * Set search
     */
    public void setSearch(List<SearchType> search) {
        this.search = search;
    }


    /**
     * Get totalResults
     */
    public String getTotalResults() {
        return totalResults;
    }

    /**
     * Set totalResults
     */
    public void setTotalResults(String totalResults) {
        this.totalResults = totalResults;
    }


    /**
     * Get response
     */
    public String getResponse() {
        return response;
    }

    /**
     * Set response
     */
    public void setResponse(String response) {
        this.response = response;
    }

    @Override
    public String toString() {
        return "OMDBSearchElementType{" +
                "search=" + search + ", " + "totalResults=" + totalResults + ", " + "response=" + response +
                "}";
    }
}
