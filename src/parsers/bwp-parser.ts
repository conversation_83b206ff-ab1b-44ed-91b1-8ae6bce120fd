import * as xml2js from 'xml2js';
import {
  BWPProcess,
  ParsedBWPProcess,
  ParsedActivity,
  ParsedVariable,
  ParsedPartnerLink,
  ParsedRestEndpoint,
  ParsedRestBinding,
  ParsedRestOperation,
  ParsedRestOperationParameter
} from '../types/index';
import { readFileContent } from '../utils/file-utils';

/**
 * BWP (BPEL Process) 文件解析器
 */
export class BWPParser {
  private namespaceMap: Map<string, string> = new Map();

  /**
   * 解析 BWP 文件
   */
  async parseBWP(filePath: string): Promise<ParsedBWPProcess> {
    const content = readFileContent(filePath);
    const bwpProcess = await this.parseXMLToBWP(content);
    
    return this.convertToParseProcess(bwpProcess);
  }

  /**
   * 将 XML 字符串解析为 BWP 对象
   */
  private async parseXMLToBWP(content: string): Promise<BWPProcess> {
    return new Promise((resolve, reject) => {
      xml2js.parseString(content, {
        explicitArray: true,
        mergeAttrs: false,
        explicitRoot: true,
        tagNameProcessors: [xml2js.processors.stripPrefix]
      }, (err, result) => {
        if (err) {
          reject(new Error(`Failed to parse BWP XML: ${err.message}`));
        } else {
          // 提取 process 节点
          const processNode = result.process;
          if (!processNode) {
            reject(new Error('No process node found in BWP file'));
            return;
          }
          resolve(processNode as BWPProcess);
        }
      });
    });
  }

  /**
   * 转换为解析后的流程对象
   */
  private convertToParseProcess(bwpProcess: BWPProcess): ParsedBWPProcess {
    // 构建命名空间映射
    this.buildNamespaceMap(bwpProcess);

    const processInfo = this.parseProcessInfo(bwpProcess);
    const processInterface = this.parseProcessInterface(bwpProcess);
    const variables = this.parseVariables(bwpProcess);
    const partnerLinks = this.parsePartnerLinks(bwpProcess);
    const activities = this.parseActivities(bwpProcess);
    const restEndpoints = this.extractRestEndpoints(partnerLinks, processInterface);

    return {
      name: this.extractClassName(bwpProcess.$.name),
      namespace: bwpProcess.$.targetNamespace,
      processInfo,
      interface: processInterface,
      activities,
      variables,
      partnerLinks,
      restEndpoints
    };
  }

  /**
   * 构建命名空间映射
   */
  private buildNamespaceMap(bwpProcess: BWPProcess): void {
    this.namespaceMap.clear();

    if (bwpProcess.namespaceRegistry && Array.isArray(bwpProcess.namespaceRegistry) && bwpProcess.namespaceRegistry[0]?.namespaceItem) {
      for (const item of bwpProcess.namespaceRegistry[0].namespaceItem) {
        this.namespaceMap.set(item.$.prefix, item.$.namespace);
      }
    }
  }

  /**
   * 解析流程信息
   */
  private parseProcessInfo(bwpProcess: BWPProcess): ParsedBWPProcess['processInfo'] {
    const info = Array.isArray(bwpProcess.processInfo) ? bwpProcess.processInfo[0]?.$ : bwpProcess.processInfo?.$;
    return {
      callable: info?.callable === 'true',
      stateless: info?.stateless === 'true',
      type: info?.type || 'IT',
      modifiers: info?.modifiers || 'public'
    };
  }

  /**
   * 解析流程接口
   */
  private parseProcessInterface(bwpProcess: BWPProcess): ParsedBWPProcess['interface'] {
    const iface = Array.isArray(bwpProcess.processInterface) ? bwpProcess.processInterface[0]?.$ : bwpProcess.processInterface?.$;
    if (!iface) {
      throw new Error('No process interface found');
    }

    const inputInfo = this.parseTypeReference(iface.input);
    const outputInfo = this.parseTypeReference(iface.output);

    return {
      inputType: inputInfo.type,
      outputType: outputInfo.type,
      inputNamespace: inputInfo.namespace,
      outputNamespace: outputInfo.namespace
    };
  }

  /**
   * 解析类型引用
   */
  private parseTypeReference(typeRef?: string): { type: string; namespace?: string } {
    if (!typeRef) {
      return { type: 'void' };
    }

    // 格式: {namespace}typeName
    const match = typeRef.match(/^\{([^}]+)\}(.+)$/);
    if (match) {
      return {
        namespace: match[1],
        type: match[2]
      };
    }

    return { type: typeRef };
  }

  /**
   * 解析变量
   */
  private parseVariables(bwpProcess: BWPProcess): ParsedVariable[] {
    const variables: ParsedVariable[] = [];

    const variablesArray = Array.isArray(bwpProcess.variables) ? bwpProcess.variables[0] : bwpProcess.variables;
    if (variablesArray?.variable) {
      for (const variable of variablesArray.variable) {
        const attrs = variable.$;
        const typeInfo = this.parseTypeReference(attrs.element || attrs.messageType);

        variables.push({
          name: attrs.name,
          type: attrs.element ? 'element' : 'messageType',
          dataType: typeInfo.type,
          namespace: typeInfo.namespace,
          isInternal: attrs['sca-bpel:internal'] === 'true',
          parameterType: attrs['tibex:parameter'] as 'in' | 'out' | undefined
        });
      }
    }

    return variables;
  }

  /**
   * 解析合作伙伴链接
   */
  private parsePartnerLinks(bwpProcess: BWPProcess): ParsedPartnerLink[] {
    const partnerLinks: ParsedPartnerLink[] = [];

    const partnerLinksArray = Array.isArray(bwpProcess.partnerLinks) ? bwpProcess.partnerLinks[0] : bwpProcess.partnerLinks;
    if (partnerLinksArray?.partnerLink) {
      for (const link of partnerLinksArray.partnerLink) {
        const attrs = link.$;
        const restBinding = this.parseRestBinding(link);

        partnerLinks.push({
          name: attrs.name,
          partnerLinkType: attrs.partnerLinkType,
          role: attrs.partnerRole || 'use',
          restBinding
        });
      }
    }

    return partnerLinks;
  }

  /**
   * 解析 REST 绑定
   */
  private parseRestBinding(partnerLink: any): ParsedRestBinding | undefined {
    try {
      const refBinding = Array.isArray(partnerLink.referenceBinding) ? partnerLink.referenceBinding[0] : partnerLink.referenceBinding;
      const binding = Array.isArray(refBinding?.binding) ? refBinding.binding[0] : refBinding?.binding;
      const scaBindingArray = binding?.referenceBinding?.['scaext:binding'];
      const scaBinding = Array.isArray(scaBindingArray) ? scaBindingArray[0] : scaBindingArray;

      if (!scaBinding || scaBinding.$['xsi:type'] !== 'rest:RestReferenceBinding') {
        return undefined;
      }

      const operations: ParsedRestOperation[] = [];
      if (scaBinding.operation) {
        const operationsArray = Array.isArray(scaBinding.operation) ? scaBinding.operation : [scaBinding.operation];
        for (const op of operationsArray) {
          const params: ParsedRestOperationParameter[] = [];

          if (op.parameters?.[0]?.parameterMapping) {
            const paramMappings = Array.isArray(op.parameters[0].parameterMapping)
              ? op.parameters[0].parameterMapping
              : [op.parameters[0].parameterMapping];

            for (const param of paramMappings) {
              params.push({
                name: param.$.parameterName,
                dataType: param.$.dataType,
                parameterType: param.$.parameterType,
                required: param.$.required === 'true'
              });
            }
          }

          operations.push({
            name: op.$.operationName,
            httpMethod: op.$.httpMethod,
            parameters: params,
            clientFormat: Array.isArray(op.clientFormat) ? op.clientFormat[0] : (op.clientFormat || 'json')
          });
        }
      }

      return {
        basePath: scaBinding.$.basePath || '/',
        path: scaBinding.$.path || '/',
        connector: scaBinding.$.connector || '',
        docBasePath: scaBinding.$.docBasePath || '',
        operations
      };
    } catch (error) {
      console.warn('Failed to parse REST binding:', error);
      return undefined;
    }
  }

  /**
   * 解析活动
   */
  private parseActivities(bwpProcess: BWPProcess): ParsedActivity[] {
    const activities: ParsedActivity[] = [];

    const scopeArray = Array.isArray(bwpProcess.scope) ? bwpProcess.scope[0] : bwpProcess.scope;
    const flowArray = Array.isArray(scopeArray?.flow) ? scopeArray.flow[0] : scopeArray?.flow;

    if (!flowArray) {
      return activities;
    }

    // 解析扩展活动
    if (flowArray.extensionActivity) {
      const extActivities = Array.isArray(flowArray.extensionActivity) ? flowArray.extensionActivity : [flowArray.extensionActivity];
      for (const extActivity of extActivities) {
        const activity = this.parseExtensionActivity(extActivity);
        if (activity) {
          activities.push(activity);
        }
      }
    }

    // 解析调用活动
    if (flowArray.invoke) {
      const invokeActivities = Array.isArray(flowArray.invoke) ? flowArray.invoke : [flowArray.invoke];
      for (const invoke of invokeActivities) {
        const activity = this.parseInvokeActivity(invoke);
        if (activity) {
          activities.push(activity);
        }
      }
    }

    return activities;
  }

  /**
   * 解析扩展活动
   */
  private parseExtensionActivity(extActivity: any): ParsedActivity | null {
    // 处理 receiveEvent (Start 活动)
    if (extActivity['tibex:receiveEvent']) {
      const receiveEvent = extActivity['tibex:receiveEvent'][0];
      return {
        id: receiveEvent.$['tibex:xpdlId'] || '',
        name: receiveEvent.$.name,
        type: 'start',
        inputVariable: receiveEvent.$.variable,
        links: this.parseActivityLinks(receiveEvent)
      };
    }

    // 处理 activityExtension (End, Log 等活动)
    if (extActivity['tibex:activityExtension']) {
      const activityExt = extActivity['tibex:activityExtension'][0];
      const config = activityExt.config?.[0]?.['bwext:BWActivity']?.[0];
      const activityTypeID = config?.$?.activityTypeID;

      let type: ParsedActivity['type'] = 'transform';
      if (activityTypeID === 'bw.internal.end') {
        type = 'end';
      } else if (activityTypeID === 'bw.generalactivities.log') {
        type = 'log';
      }

      return {
        id: activityExt.$['tibex:xpdlId'] || '',
        name: activityExt.$.name,
        type,
        inputVariable: activityExt.$.inputVariable,
        expression: activityExt.$.expression,
        expressionLanguage: activityExt.$.expressionLanguage,
        links: this.parseActivityLinks(activityExt),
        config: config ? {
          activityTypeID: config.$.activityTypeID,
          version: config.$.version
        } : undefined
      };
    }

    return null;
  }

  /**
   * 解析调用活动
   */
  private parseInvokeActivity(invoke: any): ParsedActivity | null {
    return {
      id: invoke.$['tibex:xpdlId'] || '',
      name: invoke.$.name,
      type: 'invoke',
      inputVariable: invoke.$.inputVariable,
      outputVariable: invoke.$.outputVariable,
      partnerLink: invoke.$.partnerLink,
      operation: invoke.$.operation,
      portType: invoke.$.portType,
      links: this.parseActivityLinks(invoke)
    };
  }

  /**
   * 解析活动链接
   */
  private parseActivityLinks(activity: any): { sources: string[]; targets: string[] } {
    const sources: string[] = [];
    const targets: string[] = [];

    if (activity.sources?.[0]?.source) {
      for (const source of activity.sources[0].source) {
        sources.push(source.$.linkName);
      }
    }

    if (activity.targets?.[0]?.target) {
      for (const target of activity.targets[0].target) {
        targets.push(target.$.linkName);
      }
    }

    return { sources, targets };
  }

  /**
   * 提取 REST 端点信息
   */
  private extractRestEndpoints(
    partnerLinks: ParsedPartnerLink[],
    processInterface: ParsedBWPProcess['interface']
  ): ParsedRestEndpoint[] {
    const endpoints: ParsedRestEndpoint[] = [];

    for (const link of partnerLinks) {
      if (link.restBinding) {
        for (const operation of link.restBinding.operations) {
          endpoints.push({
            path: link.restBinding.path,
            method: operation.httpMethod,
            operationName: operation.name,
            inputType: processInterface.inputType,
            outputType: processInterface.outputType,
            parameters: operation.parameters
          });
        }
      }
    }

    return endpoints;
  }

  /**
   * 提取类名
   */
  private extractClassName(processName: string): string {
    const parts = processName.split('.');
    return parts[parts.length - 1];
  }
}
