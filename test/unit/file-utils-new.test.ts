import * as fs from 'fs';
import * as path from 'path';
import {
  findXSDFiles,
  ensureDirectoryExists,
  readFileContent,
  writeFileContent,
  getRelativePath,
  normalizePath,
  getFileNameWithoutExtension,
  fileExists
} from '../../src/utils/file-utils';

// Mock fs module
jest.mock('fs');
jest.mock('path');

const mockedFs = jest.mocked(fs);
const mockedPath = jest.mocked(path);

describe('FileUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('findXSDFiles', () => {
    it('should find XSD files in directory', () => {
      const mockFiles = ['schema1.xsd', 'schema2.xsd', 'readme.txt'];
      
      mockedFs.existsSync.mockReturnValue(true);
      mockedFs.readdirSync.mockReturnValue(mockFiles as any);
      mockedFs.statSync.mockImplementation((filePath) => {
        const fileName = filePath.toString().split('/').pop();
        return {
          isDirectory: () => !fileName?.includes('.'),
          isFile: () => fileName?.includes('.')
        } as any;
      });
      
      const result = findXSDFiles('/test/schemas');
      
      expect(result).toEqual([
        '/test/schemas/schema1.xsd',
        '/test/schemas/schema2.xsd'
      ]);
      expect(mockedFs.existsSync).toHaveBeenCalledWith('/test/schemas');
      expect(mockedFs.readdirSync).toHaveBeenCalledWith('/test/schemas');
    });

    it('should handle non-existent directory', () => {
      mockedFs.existsSync.mockReturnValue(false);
      
      expect(() => findXSDFiles('/nonexistent/dir')).toThrow('Directory does not exist');
    });
  });

  describe('ensureDirectoryExists', () => {
    it('should create directory if it does not exist', () => {
      mockedFs.existsSync.mockReturnValue(false);
      mockedFs.mkdirSync.mockImplementation(() => {});
      
      ensureDirectoryExists('/new/directory');
      
      expect(mockedFs.existsSync).toHaveBeenCalledWith('/new/directory');
      expect(mockedFs.mkdirSync).toHaveBeenCalledWith('/new/directory', { recursive: true });
    });

    it('should not create directory if it already exists', () => {
      mockedFs.existsSync.mockReturnValue(true);
      mockedFs.mkdirSync.mockImplementation(() => {});
      
      ensureDirectoryExists('/existing/directory');
      
      expect(mockedFs.existsSync).toHaveBeenCalledWith('/existing/directory');
      expect(mockedFs.mkdirSync).not.toHaveBeenCalled();
    });
  });

  describe('readFileContent', () => {
    it('should read file content successfully', () => {
      const mockContent = 'File content here';
      mockedFs.existsSync.mockReturnValue(true);
      mockedFs.readFileSync.mockReturnValue(mockContent);
      
      const result = readFileContent('/test/file.txt');
      
      expect(result).toBe(mockContent);
      expect(mockedFs.readFileSync).toHaveBeenCalledWith('/test/file.txt', 'utf-8');
    });

    it('should handle non-existent file', () => {
      mockedFs.existsSync.mockReturnValue(false);
      
      expect(() => readFileContent('/nonexistent/file.txt')).toThrow('File does not exist');
    });
  });

  describe('writeFileContent', () => {
    it('should write file content successfully', () => {
      mockedFs.writeFileSync.mockImplementation(() => {});
      mockedPath.dirname.mockReturnValue('/test');
      mockedFs.existsSync.mockReturnValue(true);
      
      writeFileContent('/test/file.txt', 'content');
      
      expect(mockedFs.writeFileSync).toHaveBeenCalledWith('/test/file.txt', 'content', 'utf-8');
    });
  });

  describe('getRelativePath', () => {
    it('should return relative path', () => {
      mockedPath.relative.mockReturnValue('../../file.txt');
      
      const result = getRelativePath('/from/path', '/to/file.txt');
      
      expect(result).toBe('../../file.txt');
      expect(mockedPath.relative).toHaveBeenCalledWith('/from/path', '/to/file.txt');
    });
  });

  describe('normalizePath', () => {
    it('should normalize path', () => {
      mockedPath.normalize.mockReturnValue('/normalized/path');
      
      const result = normalizePath('/some/../path');
      
      expect(result).toBe('/normalized/path');
      expect(mockedPath.normalize).toHaveBeenCalledWith('/some/../path');
    });
  });

  describe('getFileNameWithoutExtension', () => {
    it('should return filename without extension', () => {
      mockedPath.basename.mockReturnValue('file.txt');
      mockedPath.extname.mockReturnValue('.txt');
      
      const result = getFileNameWithoutExtension('/path/to/file.txt');
      
      expect(result).toBe('file');
      expect(mockedPath.basename).toHaveBeenCalledWith('/path/to/file.txt');
      expect(mockedPath.extname).toHaveBeenCalledWith('file.txt');
    });
  });

  describe('fileExists', () => {
    it('should check if file exists', () => {
      mockedFs.existsSync.mockReturnValue(true);
      
      const result = fileExists('/test/file.txt');
      
      expect(result).toBe(true);
      expect(mockedFs.existsSync).toHaveBeenCalledWith('/test/file.txt');
    });

    it('should return false for non-existent file', () => {
      mockedFs.existsSync.mockReturnValue(false);
      
      const result = fileExists('/nonexistent/file.txt');
      
      expect(result).toBe(false);
      expect(mockedFs.existsSync).toHaveBeenCalledWith('/nonexistent/file.txt');
    });
  });
});
