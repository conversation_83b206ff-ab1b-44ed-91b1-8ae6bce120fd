import { BWPParser } from '../../src/parsers/bwp-parser';
import { BWPJavaGenerator } from '../../src/generators/bwp-java-generator';
import { JavaGenerationOptions } from '../../src/types';
import * as path from 'path';

describe('BWP Integration Tests', () => {
  let parser: BWPParser;
  let generator: BWPJavaGenerator;
  let options: JavaGenerationOptions;

  beforeEach(() => {
    parser = new BWPParser();
    options = {
      packageName: 'com.example.movies',
      outputDir: '/output',
      useJSR303Validation: true,
      useLombok: false,
      useJacksonAnnotations: true,
      includeConstructors: true,
      includeToString: true
    };
    generator = new BWPJavaGenerator(options);
  });

  describe('Real BWP File Processing', () => {
    it('should parse and generate Java code for SearchMovies.bwp', async () => {
      const bwpFilePath = path.join(__dirname, '../_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp');
      
      try {
        // Parse the BWP file
        const parsedProcess = await parser.parseBWP(bwpFilePath);
        
        // Verify basic parsing
        expect(parsedProcess).toBeDefined();
        expect(parsedProcess.name).toBe('SearchMovies');
        expect(parsedProcess.namespace).toBe('http://xmlns.example.com/20190722212639');
        
        // Verify process info
        expect(parsedProcess.processInfo.callable).toBe(true);
        expect(parsedProcess.processInfo.stateless).toBe(true);
        
        // Verify interface
        expect(parsedProcess.interface.inputType).toBe('moviesGetParameters');
        expect(parsedProcess.interface.outputType).toBe('OMDBSearchElement');
        
        // Verify variables
        expect(parsedProcess.variables.length).toBeGreaterThan(0);
        const startVar = parsedProcess.variables.find(v => v.name === 'Start');
        expect(startVar).toBeDefined();
        expect(startVar?.parameterType).toBe('in');
        
        // Verify partner links
        expect(parsedProcess.partnerLinks.length).toBeGreaterThan(0);
        const moviesLink = parsedProcess.partnerLinks.find(p => p.name === 'movies');
        expect(moviesLink).toBeDefined();
        expect(moviesLink?.restBinding).toBeDefined();
        
        // Verify REST endpoints
        expect(parsedProcess.restEndpoints.length).toBeGreaterThan(0);
        const endpoint = parsedProcess.restEndpoints[0];
        expect(endpoint.method).toBe('GET');
        expect(endpoint.path).toBe('/movies');
        
        // Generate Java code
        const controllerCode = generator.generateController(parsedProcess);
        const serviceCode = generator.generateService(parsedProcess);
        
        // Verify controller generation
        expect(controllerCode).toContain('package com.example.movies');
        expect(controllerCode).toContain('@RestController');
        expect(controllerCode).toContain('public class SearchMoviesController');
        expect(controllerCode).toContain('@GetMapping("/movies")');
        expect(controllerCode).toContain('ResponseEntity<OMDBSearchElement>');
        
        // Verify service generation
        expect(serviceCode).toContain('package com.example.movies');
        expect(serviceCode).toContain('@Service');
        expect(serviceCode).toContain('public class SearchMoviesService');
        expect(serviceCode).toContain('RestTemplate');
        expect(serviceCode).toContain('getForObject');
        
        console.log('Generated Controller:');
        console.log(controllerCode);
        console.log('\nGenerated Service:');
        console.log(serviceCode);
        
      } catch (error) {
        console.error('Error processing BWP file:', error);
        throw error;
      }
    });

    it('should handle BWP file with minimal structure', async () => {
      // Create a minimal BWP content for testing
      const minimalBWP = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process exitOnStandardFault="no"
            name="test.MinimalProcess"
            targetNamespace="http://test.com"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true" stateless="true" type="IT" modifiers="public"/>
            <tibex:ProcessInterface 
                input="{http://test.com}TestInput" 
                output="{http://test.com}TestOutput"/>
            <bpws:variables>
                <bpws:variable element="ns:TestInput" name="Start" 
                    sca-bpel:internal="true" tibex:parameter="in"/>
                <bpws:variable element="ns:TestOutput" name="End" 
                    sca-bpel:internal="true" tibex:parameter="out"/>
            </bpws:variables>
        </bpws:process>`;

      // Mock the file reading
      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(minimalBWP);

      const parsedProcess = await parser.parseBWP('/test/minimal.bwp');
      
      expect(parsedProcess).toBeDefined();
      expect(parsedProcess.name).toBe('MinimalProcess');
      expect(parsedProcess.interface.inputType).toBe('TestInput');
      expect(parsedProcess.interface.outputType).toBe('TestOutput');
      expect(parsedProcess.variables.length).toBe(2);
      expect(parsedProcess.partnerLinks.length).toBe(0);
      expect(parsedProcess.restEndpoints.length).toBe(0);
      
      // Should still be able to generate basic Java code
      const controllerCode = generator.generateController(parsedProcess);
      const serviceCode = generator.generateService(parsedProcess);
      
      expect(controllerCode).toContain('public class MinimalProcessController');
      expect(serviceCode).toContain('public class MinimalProcessService');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid XML gracefully', async () => {
      const invalidXML = 'This is not valid XML';
      
      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(invalidXML);

      await expect(parser.parseBWP('/test/invalid.bwp')).rejects.toThrow('Failed to parse BWP XML');
    });

    it('should handle missing process interface', async () => {
      const noInterfaceBWP = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process name="test.NoInterface" targetNamespace="http://test.com"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true"/>
        </bpws:process>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(noInterfaceBWP);

      await expect(parser.parseBWP('/test/nointerface.bwp')).rejects.toThrow('No process interface found');
    });
  });

  describe('Code Generation Quality', () => {
    it('should generate valid Java syntax', async () => {
      const testBWP = `<?xml version="1.0" encoding="UTF-8"?>
        <bpws:process name="test.QualityTest" targetNamespace="http://test.com"
            xmlns:bpws="http://docs.oasis-open.org/wsbpel/2.0/process/executable"
            xmlns:tibex="http://www.tibco.com/bpel/2007/extensions">
            <tibex:ProcessInfo callable="true" stateless="true"/>
            <tibex:ProcessInterface 
                input="{http://test.com}QualityInput" 
                output="{http://test.com}QualityOutput"/>
            <bpws:partnerLinks>
                <bpws:partnerLink name="testAPI" partnerLinkType="ns:TestLinkType" partnerRole="use">
                    <tibex:ReferenceBinding>
                        <tibex:binding>
                            <bwbinding:BWBaseBinding xmlns:bwbinding="http://tns.tibco.com/bw/model/core/bwbinding">
                                <referenceBinding name="testAPI" xsi:type="scact:Reference" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:scact="http://xsd.tns.tibco.com/amf/models/sca/componentType">
                                    <scaext:binding basePath="/api" path="/test" 
                                        xsi:type="rest:RestReferenceBinding" xmlns:scaext="http://xsd.tns.tibco.com/amf/models/sca/extensions" xmlns:rest="http://xsd.tns.tibco.com/bw/models/binding/rest">
                                        <operation httpMethod="POST" operationName="createTest">
                                            <parameters>
                                                <parameterMapping dataType="string" 
                                                    parameterName="test_id" 
                                                    parameterType="Path" required="true"/>
                                                <parameterMapping dataType="string" 
                                                    parameterName="filter_value" 
                                                    parameterType="Query" required="false"/>
                                            </parameters>
                                            <clientFormat>json</clientFormat>
                                        </operation>
                                    </scaext:binding>
                                </referenceBinding>
                            </bwbinding:BWBaseBinding>
                        </tibex:binding>
                    </tibex:ReferenceBinding>
                </bpws:partnerLink>
            </bpws:partnerLinks>
        </bpws:process>`;

      const { readFileContent } = require('../../src/utils/file-utils');
      readFileContent.mockReturnValue(testBWP);

      const parsedProcess = await parser.parseBWP('/test/quality.bwp');
      const controllerCode = generator.generateController(parsedProcess);
      const serviceCode = generator.generateService(parsedProcess);
      
      // Check for proper Java syntax elements
      expect(controllerCode).toMatch(/package\s+[\w.]+;/);
      expect(controllerCode).toMatch(/import\s+[\w.]+;/);
      expect(controllerCode).toMatch(/public\s+class\s+\w+Controller\s*\{/);
      expect(controllerCode).toMatch(/@RestController/);
      expect(controllerCode).toMatch(/@PostMapping\("\/test"\)/);
      expect(controllerCode).toMatch(/@PathVariable\("test_id"\)\s+String\s+testId/);
      expect(controllerCode).toMatch(/@RequestParam\("filter_value"\)\s+String\s+filterValue/);
      
      expect(serviceCode).toMatch(/package\s+[\w.]+;/);
      expect(serviceCode).toMatch(/public\s+class\s+\w+Service\s*\{/);
      expect(serviceCode).toMatch(/@Service/);
      expect(serviceCode).toMatch(/RestTemplate/);
      expect(serviceCode).toMatch(/postForObject/);
    });
  });
});

// Mock file-utils for integration tests
jest.mock('../../src/utils/file-utils');
