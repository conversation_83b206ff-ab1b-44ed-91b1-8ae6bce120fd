# TIBCO Movie Example

## 步骤 1

我正在使用 JavaScript 实现一个 Tibco BW 转 Java + Spring Boot 的 CLI 工具。现在我，需要你实现如下的功能：

- 读取 Schemas 目录下的 .xsd 文件，转换为对应的 Java 类

要求：

1. 在解析完后，应该对比 Java 的 .xsd 实现的差异？
2. 我需要你认真概念设计 JavaScript 的类目录结构实现，方便未来迁移。
3. 需要编写对应的解析相关单元测试

## 步骤 2


### 2.1 继续 （Augment）

我正在使用 JavaScript 实现一个 Tibco BW 转 Java + Spring Boot 的 CLI 工具。现在我实现了基本的 xsd 解析，需要你实现 .bwp 解析，转换为对应的 Java 逻辑代码。

1. 解析 .bwp 文件，以 test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp 为例 
2. 转换逻辑到 Java 代码中，编写对应的单元测试
3. 接着应该复制到  spring-boilerplate 项目中，看能否启动项目

请确保所有的测试都是通过的

## 步骤 3. 结合 AI 转换？？

### 真实项目测试，构建知识库！！


### 生成 AI Agent


