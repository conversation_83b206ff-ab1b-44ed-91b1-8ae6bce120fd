#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runCLI = runCLI;
const commander_1 = require("commander");
const path = __importStar(require("path"));
const chalk_1 = __importDefault(require("chalk"));
const inquirer = __importStar(require("inquirer"));
const xsd_parser_1 = require("../parsers/xsd-parser");
const java_generator_1 = require("../generators/java-generator");
const java_xsd_comparator_1 = require("../comparators/java-xsd-comparator");
const file_utils_1 = require("../utils/file-utils");
const program = new commander_1.Command();
program
    .name('tibco-convert')
    .description('Convert TIBCO BW XSD schemas to Java Spring Boot classes')
    .version('1.0.0');
program
    .command('generate')
    .description('Generate Java classes from XSD files')
    .option('-i, --input <dir>', 'Input directory containing XSD files')
    .option('-o, --output <dir>', 'Output directory for Java classes', './generated')
    .option('-p, --package <name>', 'Java package name', 'com.example.generated')
    .option('--lombok', 'Use Lombok annotations', false)
    .option('--jackson', 'Use Jackson annotations', false)
    .option('--validation', 'Use JSR-303 validation annotations', false)
    .option('--constructors', 'Generate constructors', true)
    .option('--tostring', 'Generate toString method', true)
    .option('--interactive', 'Interactive mode', false)
    .action(async (options) => {
    try {
        console.log(chalk_1.default.blue('🚀 Starting XSD to Java conversion...'));
        let config;
        if (options.interactive) {
            config = await promptForOptions(options);
        }
        else {
            config = {
                inputDir: options.input,
                outputDir: options.output,
                packageName: options.package,
                options: {
                    packageName: options.package,
                    outputDir: options.output,
                    useLombok: options.lombok,
                    useJacksonAnnotations: options.jackson,
                    useJSR303Validation: options.validation,
                    includeConstructors: options.constructors,
                    includeToString: options.tostring
                }
            };
        }
        if (!config.inputDir) {
            console.error(chalk_1.default.red('❌ Input directory is required'));
            process.exit(1);
        }
        await generateJavaClasses(config);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(chalk_1.default.red('❌ Error during generation:'), errorMessage);
        process.exit(1);
    }
});
program
    .command('compare')
    .description('Compare generated classes with existing Java implementation')
    .option('-i, --input <dir>', 'Input directory containing XSD files')
    .option('-j, --java <dir>', 'Directory containing existing Java classes')
    .option('-p, --package <name>', 'Java package name', 'com.example.generated')
    .option('-r, --report <file>', 'Output report file', './comparison-report.md')
    .action(async (options) => {
    try {
        console.log(chalk_1.default.blue('🔍 Starting XSD to Java comparison...'));
        if (!options.input || !options.java) {
            console.error(chalk_1.default.red('❌ Both input XSD directory and Java directory are required'));
            process.exit(1);
        }
        await compareWithExisting(options);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(chalk_1.default.red('❌ Error during comparison:'), errorMessage);
        process.exit(1);
    }
});
program
    .command('analyze')
    .description('Analyze XSD files and show structure')
    .option('-i, --input <dir>', 'Input directory containing XSD files')
    .action(async (options) => {
    try {
        console.log(chalk_1.default.blue('📊 Analyzing XSD files...'));
        if (!options.input) {
            console.error(chalk_1.default.red('❌ Input directory is required'));
            process.exit(1);
        }
        await analyzeXSDFiles(options.input);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(chalk_1.default.red('❌ Error during analysis:'), errorMessage);
        process.exit(1);
    }
});
/**
 * 交互式选项提示
 */
async function promptForOptions(initialOptions) {
    const questions = [
        {
            type: 'input',
            name: 'inputDir',
            message: 'Enter input directory containing XSD files:',
            default: initialOptions.input || './schemas',
            validate: (input) => input.trim().length > 0 || 'Input directory is required'
        },
        {
            type: 'input',
            name: 'outputDir',
            message: 'Enter output directory for Java classes:',
            default: initialOptions.output || './generated'
        },
        {
            type: 'input',
            name: 'packageName',
            message: 'Enter Java package name:',
            default: initialOptions.package || 'com.example.generated',
            validate: (input) => {
                const packageRegex = /^[a-zA-Z_$][a-zA-Z\d_$]*(\.[a-zA-Z_$][a-zA-Z\d_$]*)*$/;
                return packageRegex.test(input) || 'Invalid package name format';
            }
        },
        {
            type: 'confirm',
            name: 'useLombok',
            message: 'Use Lombok annotations?',
            default: initialOptions.lombok || false
        },
        {
            type: 'confirm',
            name: 'useJacksonAnnotations',
            message: 'Use Jackson annotations?',
            default: initialOptions.jackson || false
        },
        {
            type: 'confirm',
            name: 'useJSR303Validation',
            message: 'Use JSR-303 validation annotations?',
            default: initialOptions.validation || false
        },
        {
            type: 'confirm',
            name: 'includeConstructors',
            message: 'Generate constructors?',
            default: initialOptions.constructors !== false
        },
        {
            type: 'confirm',
            name: 'includeToString',
            message: 'Generate toString method?',
            default: initialOptions.tostring !== false
        }
    ];
    const answers = await inquirer.prompt(questions);
    return {
        inputDir: answers.inputDir,
        outputDir: answers.outputDir,
        packageName: answers.packageName,
        options: {
            packageName: answers.packageName,
            outputDir: answers.outputDir,
            useLombok: answers.useLombok,
            useJacksonAnnotations: answers.useJacksonAnnotations,
            useJSR303Validation: answers.useJSR303Validation,
            includeConstructors: answers.includeConstructors,
            includeToString: answers.includeToString
        }
    };
}
/**
 * 生成 Java 类
 */
async function generateJavaClasses(config) {
    console.log(chalk_1.default.yellow('📂 Searching for XSD files...'));
    const xsdFiles = (0, file_utils_1.findXSDFiles)(config.inputDir);
    if (xsdFiles.length === 0) {
        console.log(chalk_1.default.yellow('⚠️ No XSD files found in the specified directory'));
        return;
    }
    console.log(chalk_1.default.green(`✅ Found ${xsdFiles.length} XSD file(s):`));
    xsdFiles.forEach(file => {
        console.log(chalk_1.default.gray(`  - ${path.relative(process.cwd(), file)}`));
    });
    const parser = new xsd_parser_1.XSDParser();
    const generator = new java_generator_1.JavaCodeGenerator(config.options);
    let totalClasses = 0;
    for (const xsdFile of xsdFiles) {
        console.log(chalk_1.default.yellow(`\n🔍 Processing: ${path.basename(xsdFile)}`));
        try {
            const parsedClasses = await parser.parseXSD(xsdFile);
            console.log(chalk_1.default.green(`  ✅ Parsed ${parsedClasses.length} class(es)`));
            for (const parsedClass of parsedClasses) {
                console.log(chalk_1.default.gray(`    - ${parsedClass.name} (${parsedClass.fields.length} fields)`));
            }
            generator.generateAllClasses(parsedClasses);
            totalClasses += parsedClasses.length;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error(chalk_1.default.red(`  ❌ Failed to process ${xsdFile}:`), errorMessage);
        }
    }
    console.log(chalk_1.default.green(`\n🎉 Generation complete! Generated ${totalClasses} Java class(es) in ${config.outputDir}`));
}
/**
 * 与现有 Java 实现对比
 */
async function compareWithExisting(options) {
    console.log(chalk_1.default.yellow('📂 Searching for XSD files...'));
    const xsdFiles = (0, file_utils_1.findXSDFiles)(options.input);
    if (xsdFiles.length === 0) {
        console.log(chalk_1.default.yellow('⚠️ No XSD files found in the specified directory'));
        return;
    }
    const parser = new xsd_parser_1.XSDParser();
    const comparator = new java_xsd_comparator_1.JavaXSDComparator();
    const allParsedClasses = [];
    for (const xsdFile of xsdFiles) {
        console.log(chalk_1.default.yellow(`🔍 Processing: ${path.basename(xsdFile)}`));
        try {
            const parsedClasses = await parser.parseXSD(xsdFile);
            allParsedClasses.push(...parsedClasses);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error(chalk_1.default.red(`❌ Failed to process ${xsdFile}:`), errorMessage);
        }
    }
    console.log(chalk_1.default.yellow('🔍 Comparing with existing Java classes...'));
    const comparisonResults = comparator.compareWithExistingJava(allParsedClasses, options.java, options.package);
    const report = comparator.generateReport(comparisonResults);
    (0, file_utils_1.writeFileContent)(options.report, report);
    console.log(chalk_1.default.green(`✅ Comparison complete! Report saved to ${options.report}`));
    // 显示简要统计
    const totalDifferences = comparisonResults.reduce((sum, result) => sum + result.differences.length, 0);
    if (totalDifferences === 0) {
        console.log(chalk_1.default.green('🎉 No differences found! All classes are in sync.'));
    }
    else {
        console.log(chalk_1.default.yellow(`⚠️ Found ${totalDifferences} difference(s) across ${comparisonResults.length} class(es)`));
    }
}
/**
 * 分析 XSD 文件
 */
async function analyzeXSDFiles(inputDir) {
    const xsdFiles = (0, file_utils_1.findXSDFiles)(inputDir);
    if (xsdFiles.length === 0) {
        console.log(chalk_1.default.yellow('⚠️ No XSD files found in the specified directory'));
        return;
    }
    const parser = new xsd_parser_1.XSDParser();
    for (const xsdFile of xsdFiles) {
        console.log(chalk_1.default.blue(`\n📄 File: ${path.relative(process.cwd(), xsdFile)}`));
        try {
            const parsedClasses = await parser.parseXSD(xsdFile);
            for (const parsedClass of parsedClasses) {
                console.log(chalk_1.default.green(`\n  📦 Class: ${parsedClass.name}`));
                console.log(chalk_1.default.gray(`     Namespace: ${parsedClass.namespace || 'None'}`));
                console.log(chalk_1.default.gray(`     Root Element: ${parsedClass.isRootElement ? 'Yes' : 'No'}`));
                console.log(chalk_1.default.gray(`     Fields: ${parsedClass.fields.length}`));
                if (parsedClass.fields.length > 0) {
                    console.log(chalk_1.default.cyan('     Field Details:'));
                    for (const field of parsedClass.fields) {
                        const optional = field.isOptional ? 'optional' : 'required';
                        const array = field.isArray ? 'array' : 'single';
                        const complex = field.isComplexType ? 'complex' : 'simple';
                        console.log(chalk_1.default.gray(`       - ${field.name}: ${field.javaType} (${optional}, ${array}, ${complex})`));
                    }
                }
            }
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error(chalk_1.default.red(`❌ Failed to analyze ${xsdFile}:`), errorMessage);
        }
    }
}
// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
    console.error(chalk_1.default.red('❌ Unhandled Rejection at:'), promise, chalk_1.default.red('reason:'), reason);
    process.exit(1);
});
process.on('uncaughtException', (error) => {
    console.error(chalk_1.default.red('❌ Uncaught Exception:'), error);
    process.exit(1);
});
/**
 * 运行 CLI
 */
function runCLI() {
    program.parse();
}
// 如果作为模块直接运行
if (require.main === module) {
    runCLI();
}
//# sourceMappingURL=index.js.map