/**
 * XSD 类型到 Java 类型的映射
 */
export declare const XSD_TO_JAVA_TYPE_MAP: Record<string, string>;
/**
 * 将 XSD 类型转换为 Java 类型
 */
export declare function xsdTypeToJavaType(xsdType: string): string;
/**
 * 首字母大写
 */
export declare function capitalizeFirstLetter(str: string): string;
/**
 * 首字母小写（用于字段名）
 */
export declare function lowercaseFirstLetter(str: string): string;
/**
 * 转换为驼峰命名
 */
export declare function toCamelCase(str: string): string;
/**
 * 转换为帕斯卡命名（类名）
 */
export declare function toPascalCase(str: string): string;
/**
 * 判断是否为可选字段
 */
export declare function isOptionalField(minOccurs?: string): boolean;
/**
 * 判断是否为数组字段
 */
export declare function isArrayField(maxOccurs?: string): boolean;
/**
 * 提取命名空间前缀
 */
export declare function extractNamespacePrefix(targetNamespace: string): string;
/**
 * 生成有效的 Java 包名
 */
export declare function generatePackageName(basePackage: string, namespace?: string): string;
/**
 * 验证 Java 标识符
 */
export declare function isValidJavaIdentifier(name: string): boolean;
/**
 * 生成安全的 Java 字段名
 */
export declare function generateSafeFieldName(name: string): string;
/**
 * 生成安全的 Java 类名
 */
export declare function generateSafeClassName(name: string): string;
//# sourceMappingURL=type-utils.d.ts.map