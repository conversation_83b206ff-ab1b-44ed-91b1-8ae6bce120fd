/**
 * 确保目录存在，如果不存在则创建
 */
export declare function ensureDirectoryExists(dirPath: string): void;
/**
 * 读取目录中的所有 XSD 文件
 */
export declare function findXSDFiles(directory: string): string[];
/**
 * 读取文件内容
 */
export declare function readFileContent(filePath: string): string;
/**
 * 写入文件内容
 */
export declare function writeFileContent(filePath: string, content: string): void;
/**
 * 获取相对于某个基础目录的相对路径
 */
export declare function getRelativePath(from: string, to: string): string;
/**
 * 规范化路径
 */
export declare function normalizePath(filePath: string): string;
/**
 * 获取文件名（不包含扩展名）
 */
export declare function getFileNameWithoutExtension(filePath: string): string;
/**
 * 检查文件是否存在
 */
export declare function fileExists(filePath: string): boolean;
//# sourceMappingURL=file-utils.d.ts.map