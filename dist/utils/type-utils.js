"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.XSD_TO_JAVA_TYPE_MAP = void 0;
exports.xsdTypeToJavaType = xsdTypeToJavaType;
exports.capitalizeFirstLetter = capitalizeFirstLetter;
exports.lowercaseFirstLetter = lowercaseFirstLetter;
exports.toCamelCase = toCamelCase;
exports.toPascalCase = toPascalCase;
exports.isOptionalField = isOptionalField;
exports.isArrayField = isArrayField;
exports.extractNamespacePrefix = extractNamespacePrefix;
exports.generatePackageName = generatePackageName;
exports.isValidJavaIdentifier = isValidJavaIdentifier;
exports.generateSafeFieldName = generateSafeFieldName;
exports.generateSafeClassName = generateSafeClassName;
/**
 * XSD 类型到 Java 类型的映射
 */
exports.XSD_TO_JAVA_TYPE_MAP = {
    'string': 'String',
    'xs:string': 'String',
    'int': 'Integer',
    'xs:int': 'Integer',
    'integer': 'Integer',
    'xs:integer': 'Integer',
    'long': 'Long',
    'xs:long': 'Long',
    'double': 'Double',
    'xs:double': 'Double',
    'float': 'Float',
    'xs:float': 'Float',
    'boolean': 'Boolean',
    'xs:boolean': 'Boolean',
    'date': 'LocalDate',
    'xs:date': 'LocalDate',
    'dateTime': 'LocalDateTime',
    'xs:dateTime': 'LocalDateTime',
    'time': 'LocalTime',
    'xs:time': 'LocalTime',
    'decimal': 'BigDecimal',
    'xs:decimal': 'BigDecimal',
};
/**
 * 将 XSD 类型转换为 Java 类型
 */
function xsdTypeToJavaType(xsdType) {
    // 处理自定义命名空间类型
    if (xsdType.includes(':')) {
        const parts = xsdType.split(':');
        if (parts.length === 2 && parts[0] !== 'xs') {
            // 自定义类型，返回类名部分
            return capitalizeFirstLetter(parts[1]);
        }
    }
    return exports.XSD_TO_JAVA_TYPE_MAP[xsdType] || capitalizeFirstLetter(xsdType);
}
/**
 * 首字母大写
 */
function capitalizeFirstLetter(str) {
    if (!str)
        return str;
    return str.charAt(0).toUpperCase() + str.slice(1);
}
/**
 * 首字母小写（用于字段名）
 */
function lowercaseFirstLetter(str) {
    if (!str)
        return str;
    return str.charAt(0).toLowerCase() + str.slice(1);
}
/**
 * 转换为驼峰命名
 */
function toCamelCase(str) {
    return str.replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '');
}
/**
 * 转换为帕斯卡命名（类名）
 */
function toPascalCase(str) {
    return capitalizeFirstLetter(toCamelCase(str));
}
/**
 * 判断是否为可选字段
 */
function isOptionalField(minOccurs) {
    return minOccurs === '0' || !minOccurs;
}
/**
 * 判断是否为数组字段
 */
function isArrayField(maxOccurs) {
    if (!maxOccurs)
        return false;
    return maxOccurs === 'unbounded' || parseInt(maxOccurs) > 1;
}
/**
 * 提取命名空间前缀
 */
function extractNamespacePrefix(targetNamespace) {
    const parts = targetNamespace.split('/');
    const lastPart = parts[parts.length - 1];
    return lastPart.toLowerCase().replace(/[^a-zA-Z0-9]/g, '');
}
/**
 * 生成有效的 Java 包名
 */
function generatePackageName(basePackage, namespace) {
    if (!namespace)
        return basePackage;
    const namespacePart = extractNamespacePrefix(namespace);
    return `${basePackage}.${namespacePart}`;
}
/**
 * 验证 Java 标识符
 */
function isValidJavaIdentifier(name) {
    if (!name || name.length === 0)
        return false;
    // Java 关键字
    const javaKeywords = [
        'abstract', 'assert', 'boolean', 'break', 'byte', 'case', 'catch', 'char',
        'class', 'const', 'continue', 'default', 'do', 'double', 'else', 'enum',
        'extends', 'final', 'finally', 'float', 'for', 'goto', 'if', 'implements',
        'import', 'instanceof', 'int', 'interface', 'long', 'native', 'new',
        'package', 'private', 'protected', 'public', 'return', 'short', 'static',
        'strictfp', 'super', 'switch', 'synchronized', 'this', 'throw', 'throws',
        'transient', 'try', 'void', 'volatile', 'while'
    ];
    if (javaKeywords.includes(name.toLowerCase())) {
        return false;
    }
    // 检查是否以字母或下划线开头，后续字符为字母、数字或下划线
    return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name);
}
/**
 * 生成安全的 Java 字段名
 */
function generateSafeFieldName(name) {
    let safeName = toCamelCase(name);
    if (!isValidJavaIdentifier(safeName)) {
        // 如果是关键字，添加下划线前缀
        safeName = `_${safeName}`;
    }
    return lowercaseFirstLetter(safeName);
}
/**
 * 生成安全的 Java 类名
 */
function generateSafeClassName(name) {
    let safeName = toPascalCase(name);
    if (!isValidJavaIdentifier(safeName)) {
        // 如果是关键字，添加下划线前缀
        safeName = `_${safeName}`;
    }
    return safeName;
}
//# sourceMappingURL=type-utils.js.map