"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ensureDirectoryExists = ensureDirectoryExists;
exports.findXSDFiles = findXSDFiles;
exports.readFileContent = readFileContent;
exports.writeFileContent = writeFileContent;
exports.getRelativePath = getRelativePath;
exports.normalizePath = normalizePath;
exports.getFileNameWithoutExtension = getFileNameWithoutExtension;
exports.fileExists = fileExists;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
/**
 * 确保目录存在，如果不存在则创建
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
    }
}
/**
 * 读取目录中的所有 XSD 文件
 */
function findXSDFiles(directory) {
    const xsdFiles = [];
    if (!fs.existsSync(directory)) {
        throw new Error(`Directory does not exist: ${directory}`);
    }
    const files = fs.readdirSync(directory);
    for (const file of files) {
        const filePath = path.join(directory, file);
        const stat = fs.statSync(filePath);
        if (stat.isFile() && file.toLowerCase().endsWith('.xsd')) {
            xsdFiles.push(filePath);
        }
        else if (stat.isDirectory()) {
            // 递归搜索子目录
            xsdFiles.push(...findXSDFiles(filePath));
        }
    }
    return xsdFiles;
}
/**
 * 读取文件内容
 */
function readFileContent(filePath) {
    if (!fs.existsSync(filePath)) {
        throw new Error(`File does not exist: ${filePath}`);
    }
    return fs.readFileSync(filePath, 'utf-8');
}
/**
 * 写入文件内容
 */
function writeFileContent(filePath, content) {
    const directory = path.dirname(filePath);
    ensureDirectoryExists(directory);
    fs.writeFileSync(filePath, content, 'utf-8');
}
/**
 * 获取相对于某个基础目录的相对路径
 */
function getRelativePath(from, to) {
    return path.relative(from, to);
}
/**
 * 规范化路径
 */
function normalizePath(filePath) {
    return path.normalize(filePath);
}
/**
 * 获取文件名（不包含扩展名）
 */
function getFileNameWithoutExtension(filePath) {
    const basename = path.basename(filePath);
    const extname = path.extname(basename);
    return basename.slice(0, -extname.length);
}
/**
 * 检查文件是否存在
 */
function fileExists(filePath) {
    return fs.existsSync(filePath);
}
//# sourceMappingURL=file-utils.js.map