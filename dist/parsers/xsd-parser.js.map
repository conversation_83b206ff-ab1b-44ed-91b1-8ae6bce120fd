{"version": 3, "file": "xsd-parser.js", "sourceRoot": "", "sources": ["../../src/parsers/xsd-parser.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAQjC,oDAO6B;AAC7B,oDAAsD;AAEtD;;GAEG;AACH,MAAa,SAAS;IAAtB;QACU,kBAAa,GAA6B,IAAI,GAAG,EAAE,CAAC;IA6P9D,CAAC;IA3PC;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAC7B,MAAM,OAAO,GAAG,IAAA,4BAAe,EAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAEpD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAE3B,SAAS;QACT,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC9B,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBACpD,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC;QAED,QAAQ;QACR,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAC1B,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC5C,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,OAAe;QAC5C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE;gBAC1B,aAAa,EAAE,IAAI;gBACnB,UAAU,EAAE,KAAK;gBACjB,YAAY,EAAE,IAAI;gBAClB,iBAAiB,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC;aACnD,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBACjB,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,IAAI,KAAK,CAAC,wBAAwB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC3D,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,MAAmB,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,WAA2B,EAC3B,SAAkB;QAElB,MAAM,SAAS,GAAG,IAAA,kCAAqB,EAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAE5D,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;QAC5C,CAAC;QAED,MAAM,WAAW,GAAgB;YAC/B,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,SAAS,IAAI,EAAE;YAC1B,MAAM,EAAE,EAAE;YACV,aAAa,EAAE,KAAK;SACrB,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAE/C,OAAO;QACP,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;YACzB,KAAK,MAAM,QAAQ,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;oBACrB,KAAK,MAAM,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;wBACvC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;wBAC1D,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAC5B,OAAmB,EACnB,SAAkB;QAElB,MAAM,SAAS,GAAG,IAAA,kCAAqB,EAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAExD,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACtC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;YACzD,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC;YACnC,OAAO,aAAa,CAAC;QACvB,CAAC;QAED,MAAM,WAAW,GAAgB;YAC/B,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,SAAS,IAAI,EAAE;YAC1B,MAAM,EAAE,EAAE;YACV,aAAa,EAAE,IAAI;SACpB,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QAE/C,YAAY;QACZ,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACnB,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAClE,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;gBAC/E,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,aAAa;QACb,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,KAAK,MAAM,WAAW,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;gBAC9C,MAAM,mBAAmB,GAAG;oBAC1B,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACtB,QAAQ,EAAE,WAAW,CAAC,QAAQ;iBAC/B,CAAC;gBACF,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAC7C,mBAAmB,EACnB,SAAS,CACV,CAAC;gBACF,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;YAC1C,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CACxB,OAAmB,EACnB,SAAkB;QAElB,MAAM,SAAS,GAAG,IAAA,kCAAqB,EAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACxD,MAAM,UAAU,GAAG,IAAA,4BAAe,EAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,IAAA,yBAAY,EAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAElD,IAAI,QAAgB,CAAC;QACrB,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAI,WAAoC,CAAC;QAEzC,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACnB,OAAO;YACP,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,QAAQ,GAAG,IAAA,8BAAiB,EAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,UAAU;gBACV,QAAQ,GAAG,IAAA,kCAAqB,EAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACvE,aAAa,GAAG,IAAI,CAAC;gBAErB,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBAClE,IAAI,cAAc,EAAE,CAAC;oBACnB,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YAC/B,SAAS;YACT,MAAM,cAAc,GAAG,GAAG,SAAS,MAAM,CAAC;YAC1C,QAAQ,GAAG,IAAA,kCAAqB,EAAC,cAAc,CAAC,CAAC;YACjD,aAAa,GAAG,IAAI,CAAC;YAErB,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CACvC;gBACE,CAAC,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;gBAC3B,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ;aAC1C,EACD,SAAS,CACV,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,WAAW;YACX,QAAQ,GAAG,QAAQ,CAAC;QACtB,CAAC;QAED,SAAS;QACT,IAAI,OAAO,EAAE,CAAC;YACZ,QAAQ,GAAG,QAAQ,QAAQ,GAAG,CAAC;QACjC,CAAC;QAED,OAAO;YACL,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,QAAQ;YAChC,QAAQ;YACR,UAAU;YACV,OAAO;YACP,aAAa;YACb,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,QAAgB;QAC5C,iCAAiC;QACjC,6BAA6B;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,QAAgB;QACpC,MAAM,YAAY,GAAG;YACnB,QAAQ,EAAE,WAAW;YACrB,KAAK,EAAE,QAAQ;YACf,SAAS,EAAE,YAAY;YACvB,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,WAAW;YACrB,OAAO,EAAE,UAAU;YACnB,SAAS,EAAE,YAAY;YACvB,MAAM,EAAE,SAAS;YACjB,UAAU,EAAE,aAAa;YACzB,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,YAAY;SACxB,CAAC;QAEF,OAAO,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAgB;QACtC,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACjC,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACjB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,IAAY;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC;CACF;AA9PD,8BA8PC"}