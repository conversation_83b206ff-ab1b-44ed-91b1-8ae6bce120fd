import { ParsedClass } from '../types/index';
/**
 * XSD Schema 解析器
 */
export declare class XSDParser {
    private parsedClasses;
    /**
     * 解析 XSD 文件
     */
    parseXSD(filePath: string): Promise<ParsedClass[]>;
    /**
     * 将 XML 字符串解析为 Schema 对象
     */
    private parseXMLToSchema;
    /**
     * 解析复杂类型
     */
    private parseComplexType;
    /**
     * 解析根元素
     */
    private parseRootElement;
    /**
     * 解析元素为字段
     */
    private parseElement;
    /**
     * 查找复杂类型定义
     */
    private findComplexTypeByName;
    /**
     * 判断是否为内置类型
     */
    private isBuiltInType;
    /**
     * 提取类型名称（去除命名空间前缀）
     */
    private extractTypeName;
    /**
     * 获取所有解析的类
     */
    getAllParsedClasses(): ParsedClass[];
    /**
     * 根据名称获取解析的类
     */
    getParsedClassByName(name: string): ParsedClass | undefined;
}
//# sourceMappingURL=xsd-parser.d.ts.map