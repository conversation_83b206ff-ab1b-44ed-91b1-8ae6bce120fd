"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.XSDParser = void 0;
const xml2js = __importStar(require("xml2js"));
const type_utils_1 = require("../utils/type-utils");
const file_utils_1 = require("../utils/file-utils");
/**
 * XSD Schema 解析器
 */
class XSDParser {
    constructor() {
        this.parsedClasses = new Map();
    }
    /**
     * 解析 XSD 文件
     */
    async parseXSD(filePath) {
        const content = (0, file_utils_1.readFileContent)(filePath);
        const schema = await this.parseXMLToSchema(content);
        this.parsedClasses.clear();
        // 解析复杂类型
        if (schema.schema.complexType) {
            for (const complexType of schema.schema.complexType) {
                await this.parseComplexType(complexType, schema.schema.$.targetNamespace);
            }
        }
        // 解析根元素
        if (schema.schema.element) {
            for (const element of schema.schema.element) {
                await this.parseRootElement(element, schema.schema.$.targetNamespace);
            }
        }
        return Array.from(this.parsedClasses.values());
    }
    /**
     * 将 XML 字符串解析为 Schema 对象
     */
    async parseXMLToSchema(content) {
        return new Promise((resolve, reject) => {
            xml2js.parseString(content, {
                explicitArray: true,
                mergeAttrs: false,
                explicitRoot: true,
                tagNameProcessors: [xml2js.processors.stripPrefix]
            }, (err, result) => {
                if (err) {
                    reject(new Error(`Failed to parse XML: ${err.message}`));
                }
                else {
                    resolve(result);
                }
            });
        });
    }
    /**
     * 解析复杂类型
     */
    async parseComplexType(complexType, namespace) {
        const className = (0, type_utils_1.generateSafeClassName)(complexType.$.name);
        if (this.parsedClasses.has(className)) {
            return this.parsedClasses.get(className);
        }
        const parsedClass = {
            name: className,
            namespace: namespace || '',
            fields: [],
            isRootElement: false
        };
        this.parsedClasses.set(className, parsedClass);
        // 解析字段
        if (complexType.sequence) {
            for (const sequence of complexType.sequence) {
                if (sequence.element) {
                    for (const element of sequence.element) {
                        const field = await this.parseElement(element, namespace);
                        parsedClass.fields.push(field);
                    }
                }
            }
        }
        return parsedClass;
    }
    /**
     * 解析根元素
     */
    async parseRootElement(element, namespace) {
        const className = (0, type_utils_1.generateSafeClassName)(element.$.name);
        if (this.parsedClasses.has(className)) {
            const existingClass = this.parsedClasses.get(className);
            existingClass.isRootElement = true;
            return existingClass;
        }
        const parsedClass = {
            name: className,
            namespace: namespace || '',
            fields: [],
            isRootElement: true
        };
        this.parsedClasses.set(className, parsedClass);
        // 如果元素有类型引用
        if (element.$.type) {
            const referencedType = this.findComplexTypeByName(element.$.type);
            if (referencedType) {
                const referencedClass = await this.parseComplexType(referencedType, namespace);
                parsedClass.fields = referencedClass.fields;
            }
        }
        // 如果元素内嵌复杂类型
        if (element.complexType) {
            for (const complexType of element.complexType) {
                const complexTypeWithName = {
                    $: { name: className },
                    sequence: complexType.sequence
                };
                const inlineClass = await this.parseComplexType(complexTypeWithName, namespace);
                parsedClass.fields = inlineClass.fields;
            }
        }
        return parsedClass;
    }
    /**
     * 解析元素为字段
     */
    async parseElement(element, namespace) {
        const fieldName = (0, type_utils_1.generateSafeFieldName)(element.$.name);
        const isOptional = (0, type_utils_1.isOptionalField)(element.$.minOccurs);
        const isArray = (0, type_utils_1.isArrayField)(element.$.maxOccurs);
        let javaType;
        let isComplexType = false;
        let nestedClass;
        if (element.$.type) {
            // 引用类型
            if (this.isBuiltInType(element.$.type)) {
                javaType = (0, type_utils_1.xsdTypeToJavaType)(element.$.type);
            }
            else {
                // 自定义复杂类型
                javaType = (0, type_utils_1.generateSafeClassName)(this.extractTypeName(element.$.type));
                isComplexType = true;
                const referencedType = this.findComplexTypeByName(element.$.type);
                if (referencedType) {
                    nestedClass = await this.parseComplexType(referencedType, namespace);
                }
            }
        }
        else if (element.complexType) {
            // 内联复杂类型
            const inlineTypeName = `${fieldName}Type`;
            javaType = (0, type_utils_1.generateSafeClassName)(inlineTypeName);
            isComplexType = true;
            nestedClass = await this.parseComplexType({
                $: { name: inlineTypeName },
                sequence: element.complexType[0].sequence
            }, namespace);
        }
        else {
            // 默认为字符串类型
            javaType = 'String';
        }
        // 处理数组类型
        if (isArray) {
            javaType = `List<${javaType}>`;
        }
        return {
            name: fieldName,
            type: element.$.type || 'string',
            javaType,
            isOptional,
            isArray,
            isComplexType,
            nestedClass
        };
    }
    /**
     * 查找复杂类型定义
     */
    findComplexTypeByName(typeName) {
        // 这里需要从已解析的 schema 中查找，暂时返回 null
        // 实际实现中需要保存 schema 的引用并在此处查找
        return null;
    }
    /**
     * 判断是否为内置类型
     */
    isBuiltInType(typeName) {
        const builtInTypes = [
            'string', 'xs:string',
            'int', 'xs:int',
            'integer', 'xs:integer',
            'long', 'xs:long',
            'double', 'xs:double',
            'float', 'xs:float',
            'boolean', 'xs:boolean',
            'date', 'xs:date',
            'dateTime', 'xs:dateTime',
            'time', 'xs:time',
            'decimal', 'xs:decimal'
        ];
        return builtInTypes.includes(typeName);
    }
    /**
     * 提取类型名称（去除命名空间前缀）
     */
    extractTypeName(typeName) {
        if (typeName.includes(':')) {
            const parts = typeName.split(':');
            return parts[parts.length - 1];
        }
        return typeName;
    }
    /**
     * 获取所有解析的类
     */
    getAllParsedClasses() {
        return Array.from(this.parsedClasses.values());
    }
    /**
     * 根据名称获取解析的类
     */
    getParsedClassByName(name) {
        return this.parsedClasses.get(name);
    }
}
exports.XSDParser = XSDParser;
//# sourceMappingURL=xsd-parser.js.map