import { ParsedClass, JavaGenerationOptions } from '../types/index';
/**
 * Java 类代码生成器
 */
export declare class JavaCodeGenerator {
    private options;
    constructor(options: JavaGenerationOptions);
    /**
     * 生成所有 Java 类
     */
    generateAllClasses(classes: ParsedClass[]): void;
    /**
     * 生成单个 Java 类
     */
    generateClass(parsedClass: ParsedClass): void;
    /**
     * 生成 Java 代码字符串
     */
    private generateJavaCode;
    /**
     * 生成包声明
     */
    private generatePackageDeclaration;
    /**
     * 生成导入语句
     */
    private generateImports;
    /**
     * 生成类声明
     */
    private generateClassDeclaration;
    /**
     * 生成字段声明
     */
    private generateFields;
    /**
     * 生成单个字段声明
     */
    private generateFieldDeclaration;
    /**
     * 生成默认值
     */
    private generateDefaultValue;
    /**
     * 生成构造函数
     */
    private generateConstructors;
    /**
     * 生成带参数的构造函数
     */
    private generateParameterizedConstructor;
    /**
     * 生成 Getter 和 Setter 方法
     */
    private generateGettersAndSetters;
    /**
     * 生成单个字段的 Getter 和 Setter
     */
    private generateGetterAndSetter;
    /**
     * 生成 toString 方法
     */
    private generateToString;
    /**
     * 获取输出文件路径
     */
    private getOutputFilePath;
}
//# sourceMappingURL=java-generator.d.ts.map