"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.JavaCodeGenerator = void 0;
const file_utils_1 = require("../utils/file-utils");
const path = __importStar(require("path"));
/**
 * Java 类代码生成器
 */
class JavaCodeGenerator {
    constructor(options) {
        this.options = options;
    }
    /**
     * 生成所有 Java 类
     */
    generateAllClasses(classes) {
        for (const parsedClass of classes) {
            this.generateClass(parsedClass);
        }
    }
    /**
     * 生成单个 Java 类
     */
    generateClass(parsedClass) {
        const javaCode = this.generateJavaCode(parsedClass);
        const filePath = this.getOutputFilePath(parsedClass.name);
        (0, file_utils_1.writeFileContent)(filePath, javaCode);
        console.log(`Generated Java class: ${filePath}`);
    }
    /**
     * 生成 Java 代码字符串
     */
    generateJavaCode(parsedClass) {
        const imports = this.generateImports(parsedClass);
        const classDeclaration = this.generateClassDeclaration(parsedClass);
        const fields = this.generateFields(parsedClass);
        const constructors = this.generateConstructors(parsedClass);
        const gettersAndSetters = this.generateGettersAndSetters(parsedClass);
        const toString = this.generateToString(parsedClass);
        return `${this.generatePackageDeclaration()}
${imports}

/**
 * Generated from XSD schema
 * ${parsedClass.namespace ? `Namespace: ${parsedClass.namespace}` : ''}
 * ${parsedClass.isRootElement ? 'Root Element' : 'Complex Type'}
 */
${classDeclaration} {

${fields}
${constructors}
${gettersAndSetters}
${toString}
}
`;
    }
    /**
     * 生成包声明
     */
    generatePackageDeclaration() {
        return `package ${this.options.packageName};`;
    }
    /**
     * 生成导入语句
     */
    generateImports(parsedClass) {
        const imports = new Set();
        // 检查是否需要 List 导入
        for (const field of parsedClass.fields) {
            if (field.isArray) {
                imports.add('import java.util.List;');
                imports.add('import java.util.ArrayList;');
            }
            // 检查日期类型
            if (field.javaType.includes('LocalDate')) {
                imports.add('import java.time.LocalDate;');
            }
            if (field.javaType.includes('LocalDateTime')) {
                imports.add('import java.time.LocalDateTime;');
            }
            if (field.javaType.includes('LocalTime')) {
                imports.add('import java.time.LocalTime;');
            }
            if (field.javaType.includes('BigDecimal')) {
                imports.add('import java.math.BigDecimal;');
            }
        }
        // JSR-303 验证注解
        if (this.options.useJSR303Validation) {
            imports.add('import javax.validation.constraints.*;');
            imports.add('import javax.validation.Valid;');
        }
        // Jackson 注解
        if (this.options.useJacksonAnnotations) {
            imports.add('import com.fasterxml.jackson.annotation.*;');
        }
        // Lombok 注解
        if (this.options.useLombok) {
            imports.add('import lombok.*;');
        }
        return Array.from(imports).sort().join('\n');
    }
    /**
     * 生成类声明
     */
    generateClassDeclaration(parsedClass) {
        const annotations = [];
        if (this.options.useLombok) {
            annotations.push('@Data');
            annotations.push('@NoArgsConstructor');
            annotations.push('@AllArgsConstructor');
        }
        if (this.options.useJacksonAnnotations) {
            if (parsedClass.isRootElement) {
                annotations.push(`@JsonRootName("${parsedClass.name}")`);
            }
            annotations.push('@JsonIgnoreProperties(ignoreUnknown = true)');
        }
        const annotationString = annotations.length > 0
            ? annotations.join('\n') + '\n'
            : '';
        return `${annotationString}public class ${parsedClass.name}`;
    }
    /**
     * 生成字段声明
     */
    generateFields(parsedClass) {
        if (this.options.useLombok) {
            // 如果使用 Lombok，只生成字段声明
            return parsedClass.fields
                .map(field => this.generateFieldDeclaration(field))
                .join('\n\n');
        }
        return parsedClass.fields
            .map(field => this.generateFieldDeclaration(field))
            .join('\n\n');
    }
    /**
     * 生成单个字段声明
     */
    generateFieldDeclaration(field) {
        const annotations = [];
        // JSR-303 验证注解
        if (this.options.useJSR303Validation) {
            if (!field.isOptional) {
                annotations.push('    @NotNull');
            }
            if (field.javaType === 'String' && !field.isOptional) {
                annotations.push('    @NotBlank');
            }
            if (field.isComplexType) {
                annotations.push('    @Valid');
            }
        }
        // Jackson 注解
        if (this.options.useJacksonAnnotations) {
            annotations.push(`    @JsonProperty("${field.name}")`);
        }
        const annotationString = annotations.length > 0
            ? annotations.join('\n') + '\n'
            : '';
        const defaultValue = this.generateDefaultValue(field);
        const fieldDeclaration = `    private ${field.javaType} ${field.name}${defaultValue};`;
        return `${annotationString}${fieldDeclaration}`;
    }
    /**
     * 生成默认值
     */
    generateDefaultValue(field) {
        if (field.isArray) {
            return ' = new ArrayList<>()';
        }
        return '';
    }
    /**
     * 生成构造函数
     */
    generateConstructors(parsedClass) {
        if (this.options.useLombok || !this.options.includeConstructors) {
            return '';
        }
        const defaultConstructor = `
    /**
     * Default constructor
     */
    public ${parsedClass.name}() {
    }`;
        const parameterizedConstructor = this.generateParameterizedConstructor(parsedClass);
        return defaultConstructor + '\n' + parameterizedConstructor;
    }
    /**
     * 生成带参数的构造函数
     */
    generateParameterizedConstructor(parsedClass) {
        if (parsedClass.fields.length === 0) {
            return '';
        }
        const parameters = parsedClass.fields
            .map(field => `${field.javaType} ${field.name}`)
            .join(', ');
        const assignments = parsedClass.fields
            .map(field => `        this.${field.name} = ${field.name};`)
            .join('\n');
        return `
    /**
     * Parameterized constructor
     */
    public ${parsedClass.name}(${parameters}) {
${assignments}
    }`;
    }
    /**
     * 生成 Getter 和 Setter 方法
     */
    generateGettersAndSetters(parsedClass) {
        if (this.options.useLombok) {
            return '';
        }
        return parsedClass.fields
            .map(field => this.generateGetterAndSetter(field))
            .join('\n\n');
    }
    /**
     * 生成单个字段的 Getter 和 Setter
     */
    generateGetterAndSetter(field) {
        const capitalizedName = field.name.charAt(0).toUpperCase() + field.name.slice(1);
        const getter = `
    /**
     * Get ${field.name}
     */
    public ${field.javaType} get${capitalizedName}() {
        return ${field.name};
    }`;
        const setter = `
    /**
     * Set ${field.name}
     */
    public void set${capitalizedName}(${field.javaType} ${field.name}) {
        this.${field.name} = ${field.name};
    }`;
        return getter + '\n' + setter;
    }
    /**
     * 生成 toString 方法
     */
    generateToString(parsedClass) {
        if (this.options.useLombok || !this.options.includeToString) {
            return '';
        }
        const fields = parsedClass.fields
            .map(field => `"${field.name}=" + ${field.name}`)
            .join(' + ", " + ');
        return `
    @Override
    public String toString() {
        return "${parsedClass.name}{" +
                ${fields} +
                "}";
    }`;
    }
    /**
     * 获取输出文件路径
     */
    getOutputFilePath(className) {
        const packagePath = this.options.packageName.replace(/\./g, path.sep);
        const fileName = `${className}.java`;
        return path.join(this.options.outputDir, packagePath, fileName);
    }
}
exports.JavaCodeGenerator = JavaCodeGenerator;
//# sourceMappingURL=java-generator.js.map