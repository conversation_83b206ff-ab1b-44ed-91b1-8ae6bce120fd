{"version": 3, "file": "java-generator.js", "sourceRoot": "", "sources": ["../../src/generators/java-generator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA8E;AAC9E,2CAA6B;AAE7B;;GAEG;AACH,MAAa,iBAAiB;IAG5B,YAAY,OAA8B;QACxC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,OAAsB;QACvC,KAAK,MAAM,WAAW,IAAI,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,WAAwB;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE1D,IAAA,6BAAgB,EAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,WAAwB;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAClD,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;QACpE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAC5D,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;QACtE,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAEpD,OAAO,GAAG,IAAI,CAAC,0BAA0B,EAAE;EAC7C,OAAO;;;;KAIJ,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE;KAClE,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc;;EAE9D,gBAAgB;;EAEhB,MAAM;EACN,YAAY;EACZ,iBAAiB;EACjB,QAAQ;;CAET,CAAC;IACA,CAAC;IAED;;OAEG;IACK,0BAA0B;QAChC,OAAO,WAAW,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,WAAwB;QAC9C,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAElC,iBAAiB;QACjB,KAAK,MAAM,KAAK,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;YACvC,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAClB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAC7C,CAAC;YAED,SAAS;YACT,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACzC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAC7C,CAAC;YACD,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;gBAC7C,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;YACjD,CAAC;YACD,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACzC,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAC7C,CAAC;YACD,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC1C,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAED,eAAe;QACf,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;YACrC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;YACtD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;QAED,aAAa;QACb,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;YACvC,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;QAED,YAAY;QACZ,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAClC,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,WAAwB;QACvD,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1B,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YACvC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;YACvC,IAAI,WAAW,CAAC,aAAa,EAAE,CAAC;gBAC9B,WAAW,CAAC,IAAI,CAAC,kBAAkB,WAAW,CAAC,IAAI,IAAI,CAAC,CAAC;YAC3D,CAAC;YACD,WAAW,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC;YAC7C,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;YAC/B,CAAC,CAAC,EAAE,CAAC;QAEP,OAAO,GAAG,gBAAgB,gBAAgB,WAAW,CAAC,IAAI,EAAE,CAAC;IAC/D,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,WAAwB;QAC7C,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,sBAAsB;YACtB,OAAO,WAAW,CAAC,MAAM;iBACtB,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;iBAClD,IAAI,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC;QAED,OAAO,WAAW,CAAC,MAAM;aACtB,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;aAClD,IAAI,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,wBAAwB,CAAC,KAAkB;QACjD,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,eAAe;QACf,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBACtB,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YACnC,CAAC;YACD,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBACrD,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpC,CAAC;YACD,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;gBACxB,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;QAED,aAAa;QACb,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;YACvC,WAAW,CAAC,IAAI,CAAC,sBAAsB,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC;QACzD,CAAC;QAED,MAAM,gBAAgB,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC;YAC7C,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;YAC/B,CAAC,CAAC,EAAE,CAAC;QAEP,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACtD,MAAM,gBAAgB,GAAG,eAAe,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,GAAG,YAAY,GAAG,CAAC;QAEvF,OAAO,GAAG,gBAAgB,GAAG,gBAAgB,EAAE,CAAC;IAClD,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,KAAkB;QAC7C,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,OAAO,sBAAsB,CAAC;QAChC,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,WAAwB;QACnD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAChE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,kBAAkB,GAAG;;;;aAIlB,WAAW,CAAC,IAAI;MACvB,CAAC;QAEH,MAAM,wBAAwB,GAAG,IAAI,CAAC,gCAAgC,CAAC,WAAW,CAAC,CAAC;QAEpF,OAAO,kBAAkB,GAAG,IAAI,GAAG,wBAAwB,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,gCAAgC,CAAC,WAAwB;QAC/D,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM;aAClC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;aAC/C,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM;aACnC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,gBAAgB,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC;aAC3D,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,OAAO;;;;aAIE,WAAW,CAAC,IAAI,IAAI,UAAU;EACzC,WAAW;MACP,CAAC;IACL,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,WAAwB;QACxD,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC3B,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,WAAW,CAAC,MAAM;aACtB,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC,CAAC;aACjD,IAAI,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,KAAkB;QAChD,MAAM,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEjF,MAAM,MAAM,GAAG;;aAEN,KAAK,CAAC,IAAI;;aAEV,KAAK,CAAC,QAAQ,OAAO,eAAe;iBAChC,KAAK,CAAC,IAAI;MACrB,CAAC;QAEH,MAAM,MAAM,GAAG;;aAEN,KAAK,CAAC,IAAI;;qBAEF,eAAe,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,IAAI;eACrD,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI;MACnC,CAAC;QAEH,OAAO,MAAM,GAAG,IAAI,GAAG,MAAM,CAAC;IAChC,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,WAAwB;QAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM;aAC9B,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;aAChD,IAAI,CAAC,YAAY,CAAC,CAAC;QAEtB,OAAO;;;kBAGO,WAAW,CAAC,IAAI;kBAChB,MAAM;;MAElB,CAAC;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,SAAiB;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACtE,MAAM,QAAQ,GAAG,GAAG,SAAS,OAAO,CAAC;QACrC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;IAClE,CAAC;CACF;AApTD,8CAoTC"}