{"version": 3, "file": "java-xsd-comparator.js", "sourceRoot": "", "sources": ["../../src/comparators/java-xsd-comparator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAAkE;AAClE,2CAA6B;AAE7B;;GAEG;AACH,MAAa,iBAAiB;IAE5B;;OAEG;IACH,uBAAuB,CACrB,aAA4B,EAC5B,eAAuB,EACvB,WAAmB;QAEnB,MAAM,OAAO,GAAuB,EAAE,CAAC;QAEvC,KAAK,MAAM,WAAW,IAAI,aAAa,EAAE,CAAC;YACxC,MAAM,gBAAgB,GAAG,IAAI,CAAC,eAAe,CAC3C,eAAe,EACf,WAAW,EACX,WAAW,CAAC,IAAI,CACjB,CAAC;YAEF,IAAI,IAAA,uBAAU,EAAC,gBAAgB,CAAC,EAAE,CAAC;gBACjC,MAAM,mBAAmB,GAAG,IAAA,4BAAe,EAAC,gBAAgB,CAAC,CAAC;gBAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;gBAExE,OAAO,CAAC,IAAI,CAAC;oBACX,SAAS,EAAE,WAAW,CAAC,IAAI;oBAC3B,WAAW;iBACZ,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC;oBACX,SAAS,EAAE,WAAW,CAAC,IAAI;oBAC3B,WAAW,EAAE,CAAC;4BACZ,IAAI,EAAE,eAAe;4BACrB,KAAK,EAAE,EAAE;4BACT,WAAW,EAAE,cAAc,WAAW,CAAC,IAAI,iBAAiB;yBAC7D,CAAC;iBACH,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,WAAwB,EAAE,WAAmB;QAChE,MAAM,WAAW,GAAiB,EAAE,CAAC;QACrC,MAAM,UAAU,GAAG,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAC;QAElE,4BAA4B;QAC5B,KAAK,MAAM,QAAQ,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;YAC1C,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC;YAEjE,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,eAAe;oBACrB,KAAK,EAAE,QAAQ,CAAC,IAAI;oBACpB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,WAAW,EAAE,UAAU,QAAQ,CAAC,IAAI,cAAc,QAAQ,CAAC,QAAQ,4BAA4B;iBAChG,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,WAAW;gBACX,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAC/D,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAE1D,IAAI,YAAY,KAAK,UAAU,EAAE,CAAC;oBAChC,WAAW,CAAC,IAAI,CAAC;wBACf,IAAI,EAAE,eAAe;wBACrB,KAAK,EAAE,QAAQ,CAAC,IAAI;wBACpB,QAAQ,EAAE,YAAY;wBACtB,MAAM,EAAE,UAAU;wBAClB,WAAW,EAAE,UAAU,QAAQ,CAAC,IAAI,kCAAkC,YAAY,aAAa,UAAU,EAAE;qBAC5G,CAAC,CAAC;gBACL,CAAC;gBAED,OAAO;gBACP,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,iBAAiB;QACjB,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC;YAEzE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,aAAa;oBACnB,KAAK,EAAE,SAAS,CAAC,IAAI;oBACrB,MAAM,EAAE,SAAS,CAAC,IAAI;oBACtB,WAAW,EAAE,UAAU,SAAS,CAAC,IAAI,+CAA+C;iBACrF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,4BAA4B,CAAC,WAAmB;QACtD,MAAM,MAAM,GAAgB,EAAE,CAAC;QAE/B,iBAAiB;QACjB,iCAAiC;QACjC,MAAM,UAAU,GAAG,kDAAkD,CAAC;QACtE,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACvD,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAE7B,WAAW;YACX,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAEpE,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI;gBACJ,IAAI;gBACJ,WAAW;aACZ,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,WAAmB,EAAE,SAAiB;QACpE,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,aAAa;QACb,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,0DAA0D,SAAS,OAAO,EAAE,GAAG,CAAC,CAAC;QACjH,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE7C,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YACtB,MAAM,iBAAiB,GAAG,uBAAuB,CAAC;YAClD,IAAI,eAAe,CAAC;YAEpB,OAAO,CAAC,eAAe,GAAG,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBACrE,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,kBAAkB,CACxB,QAAqB,EACrB,SAAoB,EACpB,WAAyB;QAEzB,MAAM,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAElE,KAAK,MAAM,kBAAkB,IAAI,mBAAmB,EAAE,CAAC;YACrD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBACxD,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,qBAAqB;oBAC3B,KAAK,EAAE,QAAQ,CAAC,IAAI;oBACpB,QAAQ,EAAE,kBAAkB;oBAC5B,WAAW,EAAE,UAAU,QAAQ,CAAC,IAAI,4BAA4B,kBAAkB,EAAE;iBACrF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,KAAkB;QAC/C,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACtB,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACrD,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC;QAED,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;YACxB,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,IAAY;QACpC,aAAa;QACb,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAAe,EAAE,WAAmB,EAAE,SAAiB;QAC7E,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACzD,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,GAAG,SAAS,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAA2B;QACxC,IAAI,MAAM,GAAG,qCAAqC,CAAC;QAEnD,IAAI,gBAAgB,GAAG,CAAC,CAAC;QAEzB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,IAAI,aAAa,MAAM,CAAC,SAAS,MAAM,CAAC;YAE9C,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,4BAA4B,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,WAAW,MAAM,CAAC,WAAW,CAAC,MAAM,qBAAqB,CAAC;gBAEpE,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;oBACtC,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC;oBACtF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAClB,MAAM,IAAI,mBAAmB,IAAI,CAAC,QAAQ,MAAM,CAAC;oBACnD,CAAC;oBACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;wBAChB,MAAM,IAAI,iBAAiB,IAAI,CAAC,MAAM,MAAM,CAAC;oBAC/C,CAAC;oBACD,MAAM,IAAI,IAAI,CAAC;gBACjB,CAAC;gBAED,gBAAgB,IAAI,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YAChD,CAAC;QACH,CAAC;QAED,MAAM,IAAI,gBAAgB,CAAC;QAC3B,MAAM,IAAI,2BAA2B,OAAO,CAAC,MAAM,IAAI,CAAC;QACxD,MAAM,IAAI,4BAA4B,gBAAgB,IAAI,CAAC;QAE3D,IAAI,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,yDAAyD,CAAC;QACtE,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,yEAAyE,CAAC;QACtF,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAzPD,8CAyPC"}