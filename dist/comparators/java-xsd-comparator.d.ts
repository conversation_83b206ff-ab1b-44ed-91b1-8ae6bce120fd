import { ParsedClass, ComparisonResult } from '../types/index';
/**
 * Java 类与 XSD 差异对比器
 */
export declare class JavaXSDComparator {
    /**
     * 对比生成的 Java 类与现有的 Java 实现
     */
    compareWithExistingJava(parsedClasses: ParsedClass[], existingJavaDir: string, packageName: string): ComparisonResult[];
    /**
     * 对比单个类
     */
    private compareClass;
    /**
     * 从 Java 代码中提取字段信息
     */
    private extractFieldsFromJavaContent;
    /**
     * 提取字段的注解
     */
    private extractFieldAnnotations;
    /**
     * 对比注解
     */
    private compareAnnotations;
    /**
     * 获取字段期望的注解
     */
    private getExpectedAnnotations;
    /**
     * 标准化 Java 类型名称
     */
    private normalizeJavaType;
    /**
     * 获取 Java 文件路径
     */
    private getJavaFilePath;
    /**
     * 生成差异报告
     */
    generateReport(results: ComparisonResult[]): string;
}
//# sourceMappingURL=java-xsd-comparator.d.ts.map