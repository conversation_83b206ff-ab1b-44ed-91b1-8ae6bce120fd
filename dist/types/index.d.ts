/**
 * XSD Schema 相关类型定义
 */
export interface XSDSchema {
    schema: {
        $: {
            targetNamespace?: string;
            elementFormDefault?: string;
        };
        complexType?: XSDComplexType[];
        element?: XSDElement[];
    };
}
export interface XSDComplexType {
    $: {
        name: string;
    };
    sequence?: XSDSequence[];
}
export interface XSDSequence {
    element: XSDElement[];
}
export interface XSDElement {
    $: {
        name: string;
        type?: string;
        minOccurs?: string;
        maxOccurs?: string;
    };
    complexType?: XSDComplexType[];
}
/**
 * 解析后的类型信息
 */
export interface ParsedClass {
    name: string;
    namespace: string;
    fields: ParsedField[];
    isRootElement?: boolean;
}
export interface ParsedField {
    name: string;
    type: string;
    javaType: string;
    isOptional: boolean;
    isArray: boolean;
    isComplexType: boolean;
    nestedClass?: ParsedClass;
}
/**
 * Java 代码生成选项
 */
export interface JavaGenerationOptions {
    packageName: string;
    outputDir: string;
    useJSR303Validation?: boolean;
    useLombok?: boolean;
    useJacksonAnnotations?: boolean;
    includeConstructors?: boolean;
    includeToString?: boolean;
}
/**
 * CLI 配置
 */
export interface CLIConfig {
    inputDir: string;
    outputDir: string;
    packageName: string;
    options: JavaGenerationOptions;
}
/**
 * 差异对比结果
 */
export interface ComparisonResult {
    className: string;
    differences: Difference[];
}
export interface Difference {
    type: 'missing_field' | 'extra_field' | 'type_mismatch' | 'annotation_mismatch';
    field: string;
    expected?: string;
    actual?: string;
    description: string;
}
//# sourceMappingURL=index.d.ts.map